import {
  Injectable,
  Logger,
  BadRequestException,
  NotFoundException,
  InternalServerErrorException,
} from '@nestjs/common';
import { PrismaService } from '../prisma/prisma.service';
import { InitiateRedeemDto } from './dto/initiate-redeem.dto';
import { UpdateRedeemDto } from './dto/update-redeem.dto';
import { RedeemResponseDto } from './dto/redeem-response.dto';
import { UpdateRedeemResponseDto } from './dto/update-redeem-response.dto';
import {
  REDEEM_ERROR_MESSAGES,
  REDEEM_SUCCESS_MESSAGES,
  DEFAULT_VALUES,
} from './constants/redeem.constants';
import { $Enums } from '../../generated/prisma';
import { STATUS_BUY } from '../subscribe/constants/subscribe.constants';
import { WalletValidationUtil } from '../wallet/utils/wallet-validation.util';

@Injectable()
export class RedeemService {
  private readonly logger = new Logger(RedeemService.name);

  constructor(private readonly prisma: PrismaService) {}

  /**
   * Initiate a new redeem request
   * @param initiateRedeemDto - Redeem initiation data
   * @param userId - The ID of the user from token authorization
   * @returns Promise<RedeemResponseDto> - Created redeem information
   */
  async initiate(
    initiateRedeemDto: InitiateRedeemDto,
    userId: string,
  ): Promise<RedeemResponseDto> {
    try {
      this.logger.log(
        `Initiating redeem for user: ${userId} with data: ${JSON.stringify({
          subscribeIds: initiateRedeemDto.subscribe_id.map((s) => s.id),
          amount: initiateRedeemDto.amount.toString(),
        })}`,
      );

      // Validate input data
      this.validateInitiateRedeemDto(initiateRedeemDto);

      // Extract subscribe IDs from the array of objects
      const subscribeIds = initiateRedeemDto.subscribe_id.map(
        (item) => item.id,
      );

      // Check for duplicate subscribe IDs
      const uniqueIds = new Set(subscribeIds);
      if (uniqueIds.size !== subscribeIds.length) {
        throw new BadRequestException(
          REDEEM_ERROR_MESSAGES.DUPLICATE_SUBSCRIBE_IDS,
        );
      }

      // Validate all subscriptions exist and belong to the user
      await this.validateSubscriptions(subscribeIds, userId);

      // Create redeem in a transaction for data consistency
      return await this.prisma.$transaction(async (prisma) => {
        // Calculate date cutoff (30 days from now)
        const dateCutoff = new Date();
        dateCutoff.setDate(
          dateCutoff.getDate() + DEFAULT_VALUES.DATE_CUTOFF_DAYS,
        );

        const redeemData = {
          paymentStatus: DEFAULT_VALUES.PAYMENT_STATUS,
          checkoutStatus: DEFAULT_VALUES.CHECKOUT_STATUS,
          redeemStatus: DEFAULT_VALUES.REDEEM_STATUS,
          statusBuy: DEFAULT_VALUES.STATUS_BUY as $Enums.StatusBuy,
          amount: initiateRedeemDto.amount,
          totalAmount: initiateRedeemDto.amount,
          dateCutoff: dateCutoff,
        };

        this.logger.debug(
          `Creating redeem with data: ${JSON.stringify({
            ...redeemData,
            totalAmount: redeemData.totalAmount.toString(),
          })}`,
        );

        // Create the Redeem record
        const redeem = await prisma.redeem.create({
          data: redeemData,
        });

        this.logger.log(
          `Successfully created redeem ${redeem.id} for user ${userId}`,
        );

        // Create RedeemSubscribes junction records
        const redeemSubscribesData = subscribeIds.map((subscribeId) => ({
          redeemId: redeem.id,
          subscribeId: subscribeId,
        }));

        await prisma.redeemSubscribes.createMany({
          data: redeemSubscribesData,
        });

        this.logger.log(
          `Successfully created ${redeemSubscribesData.length} RedeemSubscribes records for redeem ${redeem.id}`,
        );

        // Return minimal response
        return {
          statusCode: 201,
          message: REDEEM_SUCCESS_MESSAGES.REDEEM_INITIATED,
          data: {
            id: redeem.id,
          },
        };
      });
    } catch (error) {
      return this.handleServiceError(error, 'initiate');
    }
  }

  /**
   * Validate input data for initiating redeem
   * @param initiateRedeemDto - Redeem initiation data
   * @throws BadRequestException if data is invalid
   */
  private validateInitiateRedeemDto(
    initiateRedeemDto: InitiateRedeemDto,
  ): void {
    if (!initiateRedeemDto.amount || initiateRedeemDto.amount.lte(0)) {
      throw new BadRequestException(REDEEM_ERROR_MESSAGES.INVALID_AMOUNT);
    }

    if (
      !initiateRedeemDto.subscribe_id ||
      initiateRedeemDto.subscribe_id.length === 0
    ) {
      throw new BadRequestException(REDEEM_ERROR_MESSAGES.EMPTY_SUBSCRIBE_LIST);
    }
  }

  /**
   * Validate input data for updating redeem
   * @param updateRedeemDto - Redeem update data
   * @throws BadRequestException if data is invalid
   */
  private validateUpdateRedeemDto(updateRedeemDto: UpdateRedeemDto): void {
    if (!updateRedeemDto.totalAmount || updateRedeemDto.totalAmount.lte(0)) {
      throw new BadRequestException(REDEEM_ERROR_MESSAGES.INVALID_AMOUNT);
    }

    // Validate status values if provided
    const validPaymentStatuses = ['PAID', 'UNPAID', 'PENDING', 'FAILED'];
    const validCheckoutStatuses = [
      'PREPARED',
      'PROCESSING',
      'COMPLETED',
      'FAILED',
    ];
    const validRedeemStatuses = ['ACTIVE', 'INACTIVE', 'CANCELLED', 'EXPIRED'];

    if (
      updateRedeemDto.paymentStatus &&
      !validPaymentStatuses.includes(updateRedeemDto.paymentStatus)
    ) {
      throw new BadRequestException(
        REDEEM_ERROR_MESSAGES.INVALID_PAYMENT_STATUS,
      );
    }

    if (
      updateRedeemDto.checkoutStatus &&
      !validCheckoutStatuses.includes(updateRedeemDto.checkoutStatus)
    ) {
      throw new BadRequestException(
        REDEEM_ERROR_MESSAGES.INVALID_CHECKOUT_STATUS,
      );
    }

    if (
      updateRedeemDto.redeemStatus &&
      !validRedeemStatuses.includes(updateRedeemDto.redeemStatus)
    ) {
      throw new BadRequestException(
        REDEEM_ERROR_MESSAGES.INVALID_REDEEM_STATUS,
      );
    }

    // Validate walletId if provided
    if (updateRedeemDto.walletId) {
      try {
        WalletValidationUtil.validateWalletId(updateRedeemDto.walletId);
      } catch (error) {
        throw new BadRequestException(
          `Invalid wallet ID: ${error.message}`,
        );
      }
    }

    // Validate chainId format if provided
    if (updateRedeemDto.chainId) {
      if (!updateRedeemDto.chainId.trim()) {
        throw new BadRequestException('Chain ID cannot be empty');
      }
    }

    // Validate paymentProvider if provided
    if (updateRedeemDto.paymentProvider) {
      if (!updateRedeemDto.paymentProvider.trim()) {
        throw new BadRequestException('Payment provider cannot be empty');
      }
    }

    // Validate currencies if provided
    if (updateRedeemDto.currencies) {
      if (!updateRedeemDto.currencies.trim()) {
        throw new BadRequestException('Currencies cannot be empty');
      }
    }
  }

  /**
   * Update an existing redeem
   * @param redeemId - The ID of the redeem to update
   * @param updateRedeemDto - Redeem update data
   * @param userId - The ID of the user from token authorization
   * @returns Promise<UpdateRedeemResponseDto> - Updated redeem information
   */
  async update(
    redeemId: string,
    updateRedeemDto: UpdateRedeemDto,
    userId: string,
  ): Promise<UpdateRedeemResponseDto> {
    try {
      this.logger.log(
        `Updating redeem ${redeemId} for user: ${userId} with data: ${JSON.stringify(
          {
            totalAmount: updateRedeemDto.totalAmount.toString(),
            paymentStatus: updateRedeemDto.paymentStatus,
            checkoutStatus: updateRedeemDto.checkoutStatus,
            redeemStatus: updateRedeemDto.redeemStatus,
            walletId: updateRedeemDto.walletId,
            chainId: updateRedeemDto.chainId,
            paymentProvider: updateRedeemDto.paymentProvider,
            currencies: updateRedeemDto.currencies,
          },
        )}`,
      );

      // Validate input data
      this.validateUpdateRedeemDto(updateRedeemDto);

      // Validate that the redeem exists and belongs to the user
      await this.validateRedeemOwnership(redeemId, userId);

      // Validate walletId exists if provided
      if (updateRedeemDto.walletId) {
        await this.validateWalletExists(updateRedeemDto.walletId, userId);
      }

      // Calculate status_buy and dateCutoff based on current time
      const { statusBuy, dateCutoff } = this.calculateStatusAndCutoff();

      // Update redeem in a transaction for data consistency
      return await this.prisma.$transaction(async (prisma) => {
        const updateData = {
          amount: updateRedeemDto.totalAmount,
          totalAmount: updateRedeemDto.totalAmount,
          paymentStatus: updateRedeemDto.paymentStatus || undefined,
          checkoutStatus: updateRedeemDto.checkoutStatus || undefined,
          redeemStatus: updateRedeemDto.redeemStatus || undefined,
          walletId: updateRedeemDto.walletId || undefined,
          chainId: updateRedeemDto.chainId || undefined,
          paymentProvider: updateRedeemDto.paymentProvider || undefined,
          currencies: updateRedeemDto.currencies || undefined,
          statusBuy: statusBuy as $Enums.StatusBuy,
          dateCutoff: dateCutoff,
          updatedAt: new Date(Date.now()),
        };

        this.logger.debug(
          `Updating redeem ${redeemId} with data: ${JSON.stringify(updateData)}`,
        );

        // Generate request number once per redeem (only if missing)
        const existing = await prisma.redeem.findUnique({
          where: { id: redeemId },
          select: { requestNumber: true },
        });

        let redeem: any;
        if (!existing?.requestNumber) {
          // Build today's prefix REQ-YYYYMMDD
          const now = new Date();
          const yyyy = now.getFullYear();
          const mm = String(now.getMonth() + 1).padStart(2, '0');
          const dd = String(now.getDate()).padStart(2, '0');
          const prefix = `REQ-${yyyy}${mm}${dd}`;

          // Start counter from current count to minimize retries
          const base = await prisma.redeem.count({
            where: { requestNumber: { startsWith: `${prefix}-` } },
          });

          let attempt = 0;
          const maxAttempts = 20;
          while (attempt < maxAttempts) {
            const candidate = `${prefix}-${base + 1 + attempt}`;
            try {
              redeem = await prisma.redeem.update({
                where: { id: redeemId },
                data: { ...updateData, requestNumber: candidate },
              });
              break; // success
            } catch (e: any) {
              if (e?.code === 'P2002') {
                // Unique constraint violation; try next counter
                attempt++;
                continue;
              }
              throw e;
            }
          }

          if (!redeem) {
            // Fallback (should be rare): update without setting requestNumber
            redeem = await prisma.redeem.update({
              where: { id: redeemId },
              data: updateData,
            });
          }
        } else {
          // Already has a request number; just update fields
          redeem = await prisma.redeem.update({
            where: { id: redeemId },
            data: updateData,
          });
        }

        this.logger.log(
          `Successfully updated redeem ${redeem.id} for user ${userId}`,
        );

        // Map to response DTO
        return this.mapToUpdateResponseDto(redeem);
      });
    } catch (error) {
      return this.handleServiceError(error, 'update');
    }
  }

  /**
   * Validate that all subscriptions exist and belong to the user
   * @param subscribeIds - Array of subscription IDs to validate
   * @param userId - User ID to check ownership
   * @throws NotFoundException if any subscription doesn't exist or doesn't belong to user
   */
  private async validateSubscriptions(
    subscribeIds: string[],
    userId: string,
  ): Promise<void> {
    const subscriptions = await this.prisma.subscribe.findMany({
      where: {
        id: { in: subscribeIds },
      },
      select: {
        id: true,
        userId: true,
      },
    });

    // Check if all subscriptions were found
    if (subscriptions.length !== subscribeIds.length) {
      const foundIds = subscriptions.map((s) => s.id);
      const missingIds = subscribeIds.filter((id) => !foundIds.includes(id));
      this.logger.warn(
        `Subscriptions not found: ${missingIds.join(', ')} for user ${userId}`,
      );
      throw new NotFoundException(REDEEM_ERROR_MESSAGES.SUBSCRIBE_NOT_FOUND);
    }

    // Check if all subscriptions belong to the user
    const invalidSubscriptions = subscriptions.filter(
      (s) => s.userId !== userId,
    );
    if (invalidSubscriptions.length > 0) {
      this.logger.warn(
        `User ${userId} attempted to redeem subscriptions belonging to other users: ${invalidSubscriptions.map((s) => s.id).join(', ')}`,
      );
      throw new NotFoundException(REDEEM_ERROR_MESSAGES.SUBSCRIBE_NOT_FOUND);
    }
  }

  /**
   * Validate that redeem exists and belongs to the user
   * @param redeemId - Redeem ID to validate
   * @param userId - User ID to check ownership
   * @throws NotFoundException if redeem doesn't exist or doesn't belong to user
   */
  private async validateRedeemOwnership(
    redeemId: string,
    userId: string,
  ): Promise<void> {
    const redeem = await this.prisma.redeem.findUnique({
      where: { id: redeemId },
      include: {
        redeemSubscribes: {
          include: {
            subscribe: {
              select: { userId: true },
            },
          },
        },
      },
    });

    if (!redeem) {
      throw new NotFoundException(REDEEM_ERROR_MESSAGES.REDEEM_NOT_FOUND);
    }

    // Check if all associated subscriptions belong to the user
    const allBelongToUser = redeem.redeemSubscribes.every(
      (rs) => rs.subscribe.userId === userId,
    );

    if (!allBelongToUser) {
      throw new NotFoundException(REDEEM_ERROR_MESSAGES.REDEEM_NOT_FOUND);
    }
  }

  /**
   * Validate that wallet exists and belongs to the user
   * @param walletId - Wallet ID to validate
   * @param userId - User ID to check ownership
   * @throws NotFoundException if wallet doesn't exist or doesn't belong to user
   */
  private async validateWalletExists(
    walletId: string,
    userId: string,
  ): Promise<void> {
    const wallet = await this.prisma.wallet.findUnique({
      where: { id: walletId },
      select: { id: true, userId: true },
    });

    if (!wallet) {
      throw new NotFoundException('Wallet not found');
    }

    if (wallet.userId !== userId) {
      throw new NotFoundException('Wallet not found');
    }
  }

  /**
   * Calculate status_buy and dateCutoff based on current time
   * @returns Object with statusBuy and dateCutoff
   */
  private calculateStatusAndCutoff(): { statusBuy: string; dateCutoff: Date } {
    const now = new Date();
    const currentHour = now.getHours();

    // Create a date for today at 2:00 PM
    const todayAt2PM = new Date(now);
    todayAt2PM.setHours(14, 0, 0, 0); // 2:00 PM

    // Create a date for tomorrow at 2:00 PM
    const tomorrowAt2PM = new Date(now);
    tomorrowAt2PM.setDate(now.getDate() + 1);
    tomorrowAt2PM.setHours(14, 0, 0, 0); // 2:00 PM tomorrow

    if (currentHour < 14) {
      // Before 2:00 PM
      return {
        statusBuy: STATUS_BUY.IN_PROCESS,
        dateCutoff: todayAt2PM,
      };
    } else {
      // 2:00 PM or later
      return {
        statusBuy: STATUS_BUY.QUEUED_FOR_NEXT_DAY,
        dateCutoff: tomorrowAt2PM,
      };
    }
  }

  /**
   * Map redeem entity to update response DTO
   * @param redeem - Redeem entity from database
   * @returns UpdateRedeemResponseDto
   */
  private mapToUpdateResponseDto(redeem: any): UpdateRedeemResponseDto {
    return {
      id: redeem.id,
      requestNumber: redeem.requestNumber,
      dateCutoff: redeem.dateCutoff,
      paymentStatus: redeem.paymentStatus,
      checkoutStatus: redeem.checkoutStatus,
      redeemStatus: redeem.redeemStatus,
      statusBuy: redeem.statusBuy,
      totalAmount: redeem.totalAmount,
      createdAt: redeem.createdAt,
      updatedAt: redeem.updatedAt,
      walletId: redeem.walletId,
      chainId: redeem.chainId,
      paymentProvider: redeem.paymentProvider,
      currencies: redeem.currencies,
    };
  }

  /**
   * Enhanced centralized error handling for service methods
   * @param error - The error that occurred
   * @param context - The context/method where the error occurred
   * @throws Appropriate NestJS exception based on error type
   */
  private handleServiceError(error: any, context: string): never {
    // Create detailed error context for logging
    const errorContext = {
      context,
      message: error.message,
      code: error.code,
      meta: error.meta,
      timestamp: new Date().toISOString(),
      stack: error.stack,
    };

    this.logger.error(
      `${context}: ${error.message} (${error.code || 'no_code'})`,
      JSON.stringify(errorContext),
      RedeemService.name,
    );

    // Re-throw known exceptions as-is
    if (
      error instanceof BadRequestException ||
      error instanceof NotFoundException
    ) {
      throw error;
    }

    // Handle validation errors from DTO transformation
    if (error.message?.includes('Amount must be greater than 0')) {
      throw new BadRequestException(REDEEM_ERROR_MESSAGES.INVALID_AMOUNT);
    }

    // Handle Prisma errors with detailed messages
    if (error.code) {
      switch (error.code) {
        case 'P2002': {
          const target = error.meta?.target || 'unknown field';
          throw new BadRequestException(`Duplicate redeem entry for ${target}`);
        }
        case 'P2003': {
          const field = error.meta?.field_name || 'unknown field';
          throw new BadRequestException(
            `Invalid reference data for ${field}. Please check if the referenced record exists.`,
          );
        }
        case 'P2025':
          throw new NotFoundException(REDEEM_ERROR_MESSAGES.REDEEM_NOT_FOUND);
        case 'P2000':
          throw new BadRequestException(
            'The provided value for one or more fields is too long',
          );
        case 'P2001':
          throw new NotFoundException(
            'The record searched for in the where condition does not exist',
          );
        default:
          this.logger.error(
            `Unhandled Prisma error: ${error.code}`,
            errorContext,
          );
          throw new InternalServerErrorException(
            `Database error occurred: ${error.code}`,
          );
      }
    }

    // Handle decimal conversion errors
    if (error.message?.includes('Decimal')) {
      throw new BadRequestException(
        'Invalid decimal format for amount. Please provide a valid number.',
      );
    }

    // Default to internal server error with more context
    this.logger.error('Unhandled error in redeem service', errorContext);
    throw new InternalServerErrorException(
      REDEEM_ERROR_MESSAGES.REDEEM_CREATE_FAILED,
    );
  }
}
