import { ApiProperty } from '@nestjs/swagger';
import { Decimal } from '@prisma/client/runtime/library';
import { $Enums } from '../../../generated/prisma';

export class UpdateRedeemResponseDto {
  @ApiProperty({
    description: 'Unique identifier of the redeem',
    example: '550e8400-e29b-41d4-a716-446655440000',
  })
  id: string;

  @ApiProperty({
    description:
      'Generated unique request number for this update (REQ-YYYYMMDD-counter)',
    example: 'REQ-20251007-1',
    nullable: true,
  })
  requestNumber: string | null;

  @ApiProperty({
    description: 'Date cutoff for the redeem',
    example: '2024-01-31T14:00:00.000Z',
  })
  dateCutoff: Date;

  @ApiProperty({
    description: 'Current payment status',
    example: 'UNPAID',
    enum: ['PAID', 'UNPAID', 'PENDING', 'FAILED'],
  })
  paymentStatus: string;

  @ApiProperty({
    description: 'Current checkout status',
    example: 'PREPARED',
    enum: ['PREPARED', 'PROCESSING', 'COMPLETED', 'FAILED'],
  })
  checkoutStatus: string;

  @ApiProperty({
    description: 'Current redeem status',
    example: 'ACTIVE',
    enum: ['ACTIVE', 'INACTIVE', 'CANCELLED', 'EXPIRED'],
  })
  redeemStatus: string;

  @ApiProperty({
    description: 'Status of the buy transaction',
    example: 'IN_PROCESS',
    enum: ['IN_PROCESS', 'QUEUED_FOR_NEXT_DAY'],
  })
  statusBuy: $Enums.StatusBuy;

  @ApiProperty({
    description: 'Total amount for the redeem',
    example: '100.50',
    type: 'number',
  })
  totalAmount: Decimal;

  @ApiProperty({
    description: 'Redeem creation timestamp',
    example: '2024-01-01T00:00:00.000Z',
  })
  createdAt: Date;

  @ApiProperty({
    description: 'Redeem last update timestamp',
    example: '2024-01-01T00:00:00.000Z',
  })
  updatedAt: Date;
}
