import {
  IsString,
  <PERSON><PERSON>otEmpty,
  Is<PERSON>ptional,
  ValidateBy,
  ValidationOptions,
} from 'class-validator';
import { Transform } from 'class-transformer';
import { Decimal } from '@prisma/client/runtime/library';
import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';

// Custom validator for Decimal that accepts both string and number
function IsDecimalAmount(validationOptions?: ValidationOptions) {
  return ValidateBy(
    {
      name: 'isDecimalAmount',
      validator: {
        validate(value: any): boolean {
          if (value === null || value === undefined) {
            return false;
          }

          // Accept both string and number
          if (typeof value === 'string' || typeof value === 'number') {
            try {
              const decimal = new Decimal(value.toString());
              return decimal.gt(0) && decimal.lte(999999999.99);
            } catch {
              return false;
            }
          }

          // If it's already a Decimal instance
          if (value instanceof Decimal) {
            return value.gt(0) && value.lte(999999999.99);
          }

          return false;
        },
        defaultMessage(): string {
          return 'Amount must be a positive number up to 999999999.99 with maximum 8 decimal places';
        },
      },
    },
    validationOptions,
  );
}

export class UpdateRedeemDto {
  @ApiProperty({
    description: 'Total redeem amount (must be positive)',
    example: 3500,
    type: 'number',
    minimum: 0.01,
    maximum: 999999999.99,
  })
  @IsDecimalAmount({
    message:
      'Total amount must be a positive number up to 999999999.99 with maximum 8 decimal places',
  })
  @Transform(({ value }) => {
    if (typeof value === 'string' || typeof value === 'number') {
      const decimal = new Decimal(value.toString());
      if (decimal.lte(0)) {
        throw new Error('Total amount must be greater than 0');
      }
      return decimal;
    }
    if (value instanceof Decimal) {
      return value;
    }
    throw new Error('Invalid total amount format');
  })
  totalAmount: Decimal;

  @ApiPropertyOptional({
    description: 'Payment status for the redeem',
    example: 'PAID',
    enum: ['PAID', 'UNPAID', 'PENDING', 'FAILED'],
  })
  @IsOptional()
  @IsString({ message: 'Payment status must be a string' })
  paymentStatus?: string;

  @ApiPropertyOptional({
    description: 'Checkout status for the redeem',
    example: 'COMPLETED',
    enum: ['PREPARED', 'PROCESSING', 'COMPLETED', 'FAILED'],
  })
  @IsOptional()
  @IsString({ message: 'Checkout status must be a string' })
  checkoutStatus?: string;

  @ApiPropertyOptional({
    description: 'Redeem status',
    example: 'ACTIVE',
    enum: ['ACTIVE', 'INACTIVE', 'CANCELLED', 'EXPIRED'],
  })
  @IsOptional()
  @IsString({ message: 'Redeem status must be a string' })
  redeemStatus?: string;
}
